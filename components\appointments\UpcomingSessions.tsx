"use client";

import { motion } from "framer-motion";
import SessionItem from "./SessionItem";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Calendar } from "lucide-react";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import type { Member } from "@/lib/supabase/types";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

export default function UpcomingSessions({
  sessions,
  onDelete,
}: {
  sessions: ServerFlexPackageSessionWithDetails[];
  onDelete: (
    sessionId: string,
    sessionDate: string,
    sessionTime: string
  ) => void;
}) {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4"
    >
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold tracking-tight">
          Yaklaşan Randevular
        </h2>
        <Button asChild variant="outline">
          <Link href="/appointments/new">
            <Calendar className="w-4 h-4 mr-2" /> Randevu Al
          </Link>
        </Button>
      </div>

      <div className="rounded-lg border bg-card p-3">
        {sessions.length === 0 ? (
          <div className="text-center py-6 space-y-2">
            <Calendar className="w-6 h-6 mx-auto text-muted-foreground/60" />
            <p className="text-sm text-muted-foreground">
              Yaklaşan randevun bulunmuyor
            </p>
            <Button asChild size="sm" className="rounded-md">
              <Link href="/appointments/new">Yeni Randevu Al</Link>
            </Button>
          </div>
        ) : (
          <div className="grid gap-2.5">
            {sessions.map((s, i) => (
              <SessionItem
                key={s.id}
                session={s}
                packageName={s.packageName}
                onDelete={() => onDelete(s.id, s.session_date, s.session_time)}
                canDelete={canDeleteSession(s.session_date, s.session_time)}
                index={i}
                compact
              />
            ))}
          </div>
        )}
      </div>
    </motion.section>
  );
}
