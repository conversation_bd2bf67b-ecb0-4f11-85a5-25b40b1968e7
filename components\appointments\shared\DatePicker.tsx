"use client";

import { memo } from "react";
import { cn } from "@/lib/utils";
import CalendarDatePicker from "./CalendarDatePicker";

interface DatePickerProps {
  selectedDate: string;
  onDateSelect: (date: string) => void;
  bookedDates?: string[];
  originalDate?: string; // For edit mode - to highlight original date
  isEditMode?: boolean;
}

export default function DatePicker({
  selectedDate,
  onDateSelect,
  bookedDates = [],
  originalDate,
  isEditMode = false,
}: DatePickerProps) {
  return (
    <CalendarDatePicker
      selectedDate={selectedDate}
      onDateSelect={onDateSelect}
      bookedDates={bookedDates}
      originalDate={originalDate}
      isEditMode={isEditMode}
    />
  );
}
