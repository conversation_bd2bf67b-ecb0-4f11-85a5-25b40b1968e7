"use client";

import { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { History } from "lucide-react";
import { getMemberFromPhone, getMemberSessions } from "@/lib/actions/database";
import type { Member } from "@/lib/supabase/types";
import SessionItem from "@/components/appointments/SessionItem";

interface Session {
  id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  packageName?: string;
}

export default function AppointmentHistoryPage() {
  const [member, setMember] = useState<Member | null>(null);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const phone = sessionStorage.getItem("memberPhone");
    if (!phone) {
      window.location.href = "/appointments";
      return;
    }
    (async () => {
      try {
        const m = await getMemberFromPhone(phone);
        if (!m) return;
        setMember(m);
        const all = (await getMemberSessions(m.id)) as any as Session[];
        setSessions(all);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  const completed = useMemo(
    () =>
      sessions
        .filter((s) => s.status === "completed")
        .sort(
          (a, b) =>
            new Date(b.session_date).getTime() -
            new Date(a.session_date).getTime()
        ),
    [sessions]
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          <div className="flex justify-center items-center min-h-[50vh] text-sm text-muted-foreground">
            Geçmiş yükleniyor...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="flex items-center justify-between mb-3 pt-2">
          <h1 className="text-xl font-bold">Geçmiş Randevular</h1>
          <Button asChild size="sm">
            <Link href="/appointments">Randevu Paneli</Link>
          </Button>
        </div>

        <div className="rounded-lg border bg-card p-3">
          {completed.length === 0 ? (
            <div className="text-center py-10 space-y-3">
              <History className="w-8 h-8 mx-auto text-muted-foreground/60" />
              <div className="text-sm text-muted-foreground">
                Tamamlanmış randevunuz yok
              </div>
              <Button asChild size="sm">
                <Link href="/appointments/new">Randevu Al</Link>
              </Button>
            </div>
          ) : (
            <div className="grid gap-2.5">
              {completed.map((s, i) => (
                <SessionItem
                  key={s.id}
                  session={s as any}
                  packageName={s.packageName}
                  isHistorical
                  index={i}
                  compact
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
