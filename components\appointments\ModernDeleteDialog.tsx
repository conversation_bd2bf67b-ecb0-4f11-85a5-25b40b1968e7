"use client";

import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Trash2, Calendar } from "lucide-react";

interface ModernDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  sessionInfo: string;
}

export default function ModernDeleteDialog({
  isOpen,
  onClose,
  onConfirm,
  sessionInfo,
}: ModernDeleteDialogProps) {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            onClick={(e) => e.stopPropagation()}
            className="bg-background/95 backdrop-blur-xl border border-border/50 rounded-2xl p-6 max-w-md w-full shadow-2xl"
          >
            <div className="text-center space-y-6">
              <div className="w-16 h-16 mx-auto bg-destructive/10 rounded-2xl flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-destructive" />
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-bold text-foreground">
                  Randevu Silme Onayı
                </h3>
                <p className="text-muted-foreground">
                  Bu işlem geri alınamaz. Emin misiniz?
                </p>
              </div>

              <div className="bg-destructive/5 border border-destructive/20 rounded-xl p-4">
                <div className="flex items-center gap-3 justify-center">
                  <Calendar className="w-5 h-5 text-destructive" />
                  <p className="font-medium text-foreground">{sessionInfo}</p>
                </div>
              </div>

              <div className="flex gap-3">
                <Button variant="outline" onClick={onClose} className="flex-1">
                  İptal
                </Button>
                <Button
                  variant="destructive"
                  onClick={onConfirm}
                  className="flex-1 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Sil
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
