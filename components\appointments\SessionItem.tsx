"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import {
  Calendar,
  Package,
  CheckCircle,
  Edit3,
  Trash2,
  Clock,
  Star,
  Award,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { Member } from "@/lib/supabase/types";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface SessionItemProps {
  session: ServerFlexPackageSessionWithDetails;
  packageName?: string;
  onDelete?: () => void;
  canDelete?: boolean;
  isHistorical?: boolean;
  index?: number;
  compact?: boolean;
}

const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const formatTime = (timeString: string) => timeString.slice(0, 5);

// Calculate days remaining until appointment
const calculateDaysUntil = (dateString: string): number => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const appointmentDate = new Date(dateString);
  appointmentDate.setHours(0, 0, 0, 0);
  const diffTime = appointmentDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

// Get color based on days remaining
const getDaysUntilColor = (days: number): string => {
  if (days === 0) return "text-rose-600 dark:text-rose-400";
  if (days === 1) return "text-amber-600 dark:text-amber-400";
  if (days <= 3) return "text-yellow-600 dark:text-yellow-400";
  return "text-emerald-600 dark:text-emerald-400";
};

export default function SessionItem({
  session,
  packageName,
  onDelete,
  canDelete,
  isHistorical = false,
  index = 0,
  compact = false,
}: SessionItemProps) {
  const isCompleted = session.status === "completed";
  const daysUntil = calculateDaysUntil(session.session_date);
  const daysUntilColor = getDaysUntilColor(daysUntil);

  // Format days until text
  const formatDaysUntilText = (): string => {
    if (daysUntil === 0) return "Bugün";
    if (daysUntil === 1) return "Yarın";
    return `${daysUntil} gün sonra`;
  };

  return (
    <motion.div
      initial={isHistorical ? { opacity: 0, x: -20 } : { opacity: 1, x: 0 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{
        duration: 0.4,
        ease: "easeOut",
        delay: isHistorical ? index * 0.1 : 0,
      }}
      className={cn(
        "group relative overflow-hidden rounded-xl border transition-all duration-300 hover:shadow-md",
        isCompleted
          ? "bg-gradient-to-r from-emerald-50/50 to-emerald-50/20 border-emerald-200/40 dark:from-emerald-950/20 dark:to-emerald-950/10 dark:border-emerald-800/20"
          : "bg-gradient-to-r from-blue-50/50 to-blue-50/20 border-blue-200/40 dark:from-blue-950/20 dark:to-blue-950/10 dark:border-blue-800/20"
      )}
    >
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between p-3 md:p-4 gap-3">
        <div className="flex items-center gap-5">
          <div
            className={cn(
              "relative w-10 h-10 rounded-lg flex items-center justify-center shadow-sm",
              isCompleted
                ? "bg-gradient-to-br from-emerald-500 to-emerald-600 text-white"
                : "bg-gradient-to-br from-blue-500 to-blue-600 text-white"
            )}
          >
            {isCompleted ? (
              <CheckCircle className="w-7 h-7" />
            ) : (
              <Calendar className="w-7 h-7" />
            )}
          </div>
          <div className="space-y-2">
            <div className="flex flex-wrap items-center gap-3">
              <p className="font-semibold text-foreground text-base">
                {formatDate(session.session_date)}
              </p>
              <span className="w-1 h-1 rounded-full bg-muted-foreground/40 hidden md:block" />
              <p className="font-medium text-muted-foreground text-base">
                {formatTime(session.session_time)}
              </p>
              {!isCompleted && (
                <Badge
                  variant="secondary"
                  className={cn(
                    "font-medium py-0.5 px-2.5 text-xs",
                    daysUntil === 0
                      ? "bg-rose-100 text-rose-700 border-rose-300 dark:bg-rose-950/30 dark:text-rose-400 dark:border-rose-800"
                      : daysUntil === 1
                      ? "bg-amber-100 text-amber-700 border-amber-300 dark:bg-amber-950/30 dark:text-amber-400 dark:border-amber-800"
                      : daysUntil <= 3
                      ? "bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-950/30 dark:text-yellow-400 dark:border-yellow-800"
                      : "bg-emerald-100 text-emerald-700 border-emerald-300 dark:bg-emerald-950/30 dark:text-emerald-400 dark:border-emerald-800"
                  )}
                >
                  <Clock className="w-4 h-4 mr-1" />
                  {formatDaysUntilText()}
                </Badge>
              )}
              {isCompleted && (
                <Badge
                  variant="secondary"
                  className="bg-emerald-100 text-emerald-700 border-emerald-300 dark:bg-emerald-950/30 dark:text-emerald-400 dark:border-emerald-800 py-0.5 px-2.5 text-xs"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Tamamlandı
                </Badge>
              )}
            </div>
            <p className="text-base text-muted-foreground flex items-center gap-2">
              <Package className="w-5 h-5" />
              <span>{packageName || session.packageName}</span>
            </p>
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-3 w-full md:w-auto">
          {!isCompleted && canDelete && (
            <div className="flex items-center gap-3">
              <Link href={`/appointments/${session.id}/edit`}>
                <Button
                  variant="outline"
                  size="sm"
                  className="hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/30 text-sm py-1.5 px-3"
                >
                  <Edit3 className="w-4 h-4 mr-2" /> Düzenle
                </Button>
              </Link>
              <Button
                variant="outline"
                size="sm"
                onClick={onDelete}
                className="text-destructive hover:text-destructive hover:bg-destructive/10 hover:border-destructive/30 text-sm py-1.5 px-3"
              >
                <Trash2 className="w-4 h-4 mr-2" /> Sil
              </Button>
            </div>
          )}
          {!isCompleted && !canDelete && (
            <Badge
              variant="outline"
              className="text-sm bg-amber-50 text-amber-700 border-amber-300 dark:bg-amber-950/20 dark:text-amber-400 dark:border-amber-800 py-2 px-3"
            >
              <Clock className="w-4 h-4 mr-2" />
              12 saatten az kaldı
            </Badge>
          )}
          {isCompleted && (
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 text-emerald-600 dark:text-emerald-400">
                <Star className="w-5 h-5 fill-current" />
                <span className="text-sm font-semibold">Mükemmel</span>
              </div>
              <Badge
                variant="secondary"
                className="bg-emerald-100 text-emerald-700 border-emerald-300 dark:bg-emerald-950/30 dark:text-emerald-400 dark:border-emerald-800 py-1 px-3 text-sm"
              >
                <Award className="w-4 h-4 mr-1" />
                Başarılı
              </Badge>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
