"use client";

import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Calendar } from "lucide-react";
import type { Member } from "@/lib/supabase/types";

const fadeInUp = {
  initial: { opacity: 0, y: 30 },
  animate: { opacity: 1, y: 0 },
};

interface HeroSectionProps {
  memberInfo: Member | null;
}

export default function HeroSection({ memberInfo }: HeroSectionProps) {
  return (
    <motion.section
      variants={fadeInUp}
      initial="initial"
      animate="animate"
      className="text-center space-y-6 pb-4"
    >
      <div className="space-y-3">
        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <PERSON><PERSON> gel<PERSON>, {memberInfo?.first_name || "Üye"}!
          <span className="block modern-gradient-text text-balance">
            Randevularını burada yönet
          </span>
        </motion.h1>
        <p className="hero-subtitle max-w-2xl mx-auto">
          Aktif paketlerini görüntüle, yeni randevu al ve geçmiş başarılarını
          takip et.
        </p>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-2"
      >
        <Link href="/appointments/new">
          <Button size="lg" variant="gradient" className="px-8 py-6 rounded-xl">
            <Calendar className="w-5 h-5 mr-2" />
            Yeni Randevu Al
          </Button>
        </Link>
      </motion.div>
    </motion.section>
  );
}
