"use client";

import { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import {
  getMemberFromPhone,
  getMemberSessions,
  getMemberScheduledSessions,
} from "@/lib/actions/database";
import type { Member } from "@/lib/supabase/types";
import CalendarDatePicker from "@/components/appointments/shared/CalendarDatePicker";
import SessionItem from "@/components/appointments/SessionItem";

interface Session {
  id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  member_package?: { flex_package?: { name?: string | null } | null } | null;
  packageName?: string;
}

const toISODate = (d: Date) => new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate())).toISOString().split("T")[0];

function startOfWeek(date: Date) {
  const d = new Date(date);
  const day = (d.getDay() + 6) % 7; // Mon=0 ... Sun=6
  d.setDate(d.getDate() - day);
  d.setHours(0, 0, 0, 0);
  return d;
}

function getWeekDays(base: Date): string[] {
  const start = startOfWeek(base);
  return Array.from({ length: 7 }, (_, i) => {
    const d = new Date(start);
    d.setDate(start.getDate() + i);
    return toISODate(d);
  });
}

export default function AppointmentsCalendarPage() {
  const [memberPhone, setMemberPhone] = useState<string>("");
  const [member, setMember] = useState<Member | null>(null);
  const [allSessions, setAllSessions] = useState<Session[]>([]);
  const [bookedDates, setBookedDates] = useState<string[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>(toISODate(new Date()));
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const phone = sessionStorage.getItem("memberPhone");
    if (!phone) {
      window.location.href = "/appointments";
      return;
    }
    setMemberPhone(phone);
    (async () => {
      try {
        const m = await getMemberFromPhone(phone);
        if (!m) return;
        setMember(m);
        const [sessions, booked] = await Promise.all([
          getMemberSessions(m.id),
          getMemberScheduledSessions(m.id),
        ]);
        setAllSessions(sessions as any);
        setBookedDates(booked);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  const weekDays = useMemo(() => getWeekDays(new Date(selectedDate)), [selectedDate]);

  const sessionsByDay = useMemo(() => {
    const map: Record<string, Session[]> = {};
    for (const day of weekDays) map[day] = [];
    for (const s of allSessions) {
      if (map[s.session_date]) map[s.session_date].push(s);
    }
    // sort by time
    for (const day of weekDays) {
      map[day].sort((a, b) => a.session_time.localeCompare(b.session_time));
    }
    return map;
  }, [allSessions, weekDays]);

  const goPrevWeek = () => {
    const d = new Date(selectedDate);
    d.setDate(d.getDate() - 7);
    setSelectedDate(toISODate(d));
  };
  const goNextWeek = () => {
    const d = new Date(selectedDate);
    d.setDate(d.getDate() + 7);
    setSelectedDate(toISODate(d));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          <div className="flex justify-center items-center min-h-[50vh] text-sm text-muted-foreground">
            Takvim yükleniyor...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 max-w-6xl">
        <div className="flex items-center justify-between mb-3 pt-2">
          <h1 className="text-xl font-bold">Takvim</h1>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={goPrevWeek}>
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={goNextWeek}>
              <ChevronRight className="w-4 h-4" />
            </Button>
            <Button asChild size="sm">
              <Link href="/appointments/new">
                <CalendarIcon className="w-4 h-4 mr-2" /> Randevu Al
              </Link>
            </Button>
          </div>
        </div>

        <div className="rounded-lg border bg-card p-3 mb-3">
          <CalendarDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            bookedDates={bookedDates}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3">
          {weekDays.map((day) => {
            const dateObj = new Date(day);
            const dayLabel = dateObj.toLocaleDateString("tr-TR", {
              weekday: "short",
              day: "2-digit",
              month: "2-digit",
            });
            const items = sessionsByDay[day] || [];
            return (
              <div key={day} className="rounded-lg border bg-card p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-semibold">{dayLabel}</div>
                  <div className="text-xs text-muted-foreground">
                    {items.length} kayıt
                  </div>
                </div>
                <div className="space-y-2">
                  {items.length === 0 ? (
                    <div className="text-xs text-muted-foreground py-4 text-center">
                      Kayıt yok
                    </div>
                  ) : (
                    items.map((s, idx) => (
                      <SessionItem
                        key={s.id}
                        session={s as any}
                        packageName={s.packageName}
                        index={idx}
                        compact
                      />
                    ))
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

