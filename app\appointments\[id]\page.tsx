"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import {
  getMemberFromPhone,
  getSessionWithDetails,
  deleteSession,
} from "@/lib/actions/database";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import ModernDeleteDialog from "@/components/appointments/ModernDeleteDialog";
import {
  Calendar,
  Clock,
  Package,
  ArrowLeft,
  Edit3,
  Trash2,
  CheckCircle,
} from "lucide-react";

interface SessionDetails {
  id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  member_package?: { flex_package?: { name?: string | null } | null } | null;
}

const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const formatTime = (timeString: string) => timeString?.slice(0, 5);

export default function AppointmentDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params?.id as string;

  const [session, setSession] = useState<SessionDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [confirmDelete, setConfirmDelete] = useState(false);

  useEffect(() => {
    const phone = sessionStorage.getItem("memberPhone");
    if (!phone) {
      router.push("/appointments");
      return;
    }
    (async () => {
      try {
        const member = await getMemberFromPhone(phone);
        if (!member) {
          router.push("/appointments");
          return;
        }
        const s = await getSessionWithDetails(sessionId, member.id);
        setSession(s as any);
      } finally {
        setLoading(false);
      }
    })();
  }, [sessionId, router]);

  const handleDelete = async () => {
    if (!session) return;
    const ok = await deleteSession(session.id);
    setConfirmDelete(false);
    if (ok) router.push("/appointments");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          <div className="flex justify-center items-center min-h-[50vh] text-sm text-muted-foreground">
            Randevu yükleniyor...
          </div>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4 max-w-3xl">
          <div className="rounded-lg border bg-card p-6 text-center space-y-4">
            <div className="text-base">Randevu bulunamadı.</div>
            <Button asChild>
              <Link href="/appointments">Randevulara dön</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const isCompleted = session.status === "completed";
  const editable =
    !isCompleted &&
    canDeleteSession(session.session_date, session.session_time);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 max-w-3xl space-y-3">
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-md bg-secondary">
              {isCompleted ? (
                <CheckCircle className="w-5 h-5 text-emerald-600" />
              ) : (
                <Calendar className="w-5 h-5 text-foreground/80" />
              )}
            </div>
            <div>
              <h1 className="text-xl font-bold">Randevu Detayı</h1>
              <p className="text-xs text-muted-foreground">ID: {session.id}</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/appointments")}
            className="gap-2"
          >
            <ArrowLeft className="w-4 h-4" /> Geri
          </Button>
        </div>

        <div className="rounded-lg border bg-card p-4 space-y-3">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="w-4 h-4" />{" "}
              {formatDate(session.session_date)}
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4" /> {formatTime(session.session_time)}
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Package className="w-4 h-4" />{" "}
              {session.member_package?.flex_package?.name || "Esnek Paket"}
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className={
                  isCompleted
                    ? "bg-emerald-100 text-emerald-700 border-emerald-300"
                    : ""
                }
              >
                {isCompleted ? "Tamamlandı" : "Planlandı"}
              </Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {editable ? (
            <>
              <Button asChild size="sm">
                <Link
                  href={`/appointments/${session.id}/edit`}
                  className="flex items-center gap-2"
                >
                  <Edit3 className="w-4 h-4" /> Düzenle
                </Link>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-destructive border-destructive/30"
                onClick={() => setConfirmDelete(true)}
              >
                <Trash2 className="w-4 h-4 mr-2" /> Sil
              </Button>
            </>
          ) : (
            <Badge variant="outline" className="text-xs">
              12 saatten az kaldı veya tamamlandı.
            </Badge>
          )}
        </div>
      </div>

      {confirmDelete && (
        <ModernDeleteDialog
          isOpen={confirmDelete}
          onClose={() => setConfirmDelete(false)}
          onConfirm={handleDelete}
          sessionInfo={`${formatDate(session.session_date)} - ${formatTime(
            session.session_time
          )}`}
        />
      )}
    </div>
  );
}
