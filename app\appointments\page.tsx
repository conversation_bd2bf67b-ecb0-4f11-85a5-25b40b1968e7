"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
  getMemberActivePackages,
  getMemberSessions,
  deleteSession,
} from "@/lib/actions/database";
import type {
  Member,
  MemberFlexPackageWithDetails,
} from "@/lib/supabase/types";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import Link from "next/link";

// Import modular components
import DashboardHeader from "@/components/appointments/DashboardHeader";
import UpcomingSessions from "@/components/appointments/UpcomingSessions";
import StatsGrid from "@/components/appointments/StatsGrid";
import PackagesSection from "@/components/appointments/PackagesSection";
import CompletedSessionsTimeline from "@/components/appointments/CompletedSessionsTimeline";
import ModernLoadingScreen from "@/components/appointments/ModernLoadingScreen";
import ModernDeleteDialog from "@/components/appointments/ModernDeleteDialog";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

// Type definitions
interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface FlexPackageGroup {
  packageName: string;
  sessions: ServerFlexPackageSessionWithDetails[];
  memberPackage: MemberFlexPackageWithDetails;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  expiryDate: string;
}

interface FlexDashboardStats {
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  scheduledSessions: number;
  completedSessions: number;
  activePackages: number;
}

// Animation variants
const staggerChildren = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Format utilities
const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const formatTime = (timeString: string) => timeString.slice(0, 5);

// Main component
export default function FlexAppointmentsPage() {
  const [userPhone, setUserPhone] = useState<string | null>(null);

  useEffect(() => {
    const storedPhone = sessionStorage.getItem("memberPhone");
    if (storedPhone) {
      setUserPhone(storedPhone);
    }
  }, []);

  // The layout will handle phone gate, so we just render the dashboard
  if (!userPhone) {
    return null; // Layout will show phone gate
  }

  return <ModernDashboard phone={userPhone} />;
}

// Modern Dashboard Component - Refactored with modular components
function ModernDashboard({ phone }: { phone: string }) {
  const [memberInfo, setMemberInfo] = useState<Member | null>(null);
  const [flexPackages, setFlexPackages] = useState<FlexPackageGroup[]>([]);
  const [stats, setStats] = useState<FlexDashboardStats>({
    totalSessions: 0,
    usedSessions: 0,
    remainingSessions: 0,
    scheduledSessions: 0,
    completedSessions: 0,
    activePackages: 0,
  });
  const [expandedPackages, setExpandedPackages] = useState<
    Record<string, boolean>
  >({});
  const [loading, setLoading] = useState(true);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    show: boolean;
    sessionId: string;
    sessionInfo?: string;
  }>({ show: false, sessionId: "", sessionInfo: "" });

  // Optimized data fetching with useMemo
  const fetchFlexData = useCallback(async () => {
    try {
      setLoading(true);
      await autoCompletePastSessions();
      const member = await getMemberFromPhone(phone);
      if (!member) {
        toast.error("Üye bulunamadı");
        setLoading(false);
        return;
      }
      setMemberInfo(member);

      // Fetch all data in parallel
      const [activePackages, allSessions] = await Promise.all([
        getMemberActivePackages(member.id),
        getMemberSessions(member.id),
      ]);

      // Process packages with sessions in one pass
      const packagesWithSessions: FlexPackageGroup[] = activePackages.map(
        (memberPackage) => {
          const packageSessions = allSessions.filter(
            (session) => session.member_package_id === memberPackage.id
          );

          return {
            packageName: memberPackage.flex_package?.name || "Esnek Paket",
            sessions: packageSessions as ServerFlexPackageSessionWithDetails[],
            memberPackage,
            totalSessions: memberPackage.total_sessions,
            usedSessions: memberPackage.used_sessions,
            remainingSessions:
              memberPackage.total_sessions - memberPackage.used_sessions,
            expiryDate: memberPackage.expiry_date,
          };
        }
      );

      // Calculate stats in one pass
      const totalStats = packagesWithSessions.reduce(
        (acc, pkg) => {
          acc.totalSessions += pkg.totalSessions;
          acc.usedSessions += pkg.usedSessions;
          acc.remainingSessions += pkg.remainingSessions;
          acc.scheduledSessions += pkg.sessions.filter(
            (s) => s.status === "scheduled"
          ).length;
          acc.completedSessions += pkg.sessions.filter(
            (s) => s.status === "completed"
          ).length;
          return acc;
        },
        {
          totalSessions: 0,
          usedSessions: 0,
          remainingSessions: 0,
          scheduledSessions: 0,
          completedSessions: 0,
          activePackages: activePackages.length,
        }
      );

      setFlexPackages(packagesWithSessions);
      setStats(totalStats);
    } catch (err) {
      console.error("Error fetching flex data:", err);
      toast.error("Esnek paket verileri yüklenirken hata oluştu");
    } finally {
      setLoading(false);
    }
  }, [phone]);

  useEffect(() => {
    fetchFlexData();
  }, [phone, fetchFlexData]);

  const togglePackageExpansion = (packageId: string) => {
    setExpandedPackages((prev) => ({ ...prev, [packageId]: !prev[packageId] }));
  };

  const handleDeleteSession = (
    sessionId: string,
    sessionDate: string,
    sessionTime: string
  ) => {
    if (!canDeleteSession(sessionDate, sessionTime)) {
      toast.error("Randevu saatine 12 saatten az kaldığı için silinemez.");
      return;
    }
    setDeleteConfirmation({
      show: true,
      sessionId,
      sessionInfo: `${formatDate(sessionDate)} - ${formatTime(sessionTime)}`,
    });
  };

  const confirmDeleteSession = async () => {
    try {
      const success = await deleteSession(deleteConfirmation.sessionId);
      if (success) {
        toast.success("Randevu başarıyla silindi.");
        fetchFlexData();
      } else {
        toast.error("Randevu silinirken bir hata oluştu.");
      }
    } catch (error) {
      console.error("Error deleting session:", error);
      toast.error("Randevu silinirken bir hata oluştu.");
    } finally {
      setDeleteConfirmation({ show: false, sessionId: "", sessionInfo: "" });
    }
  };

  // Memoize completed sessions calculation
  const allCompletedSessions = useMemo(() => {
    return flexPackages
      .flatMap((p) =>
        p.sessions
          .filter((s) => s.status === "completed")
          .map((s) => ({ ...s, packageName: p.packageName }))
      )
      .sort(
        (a, b) =>
          new Date(b.session_date).getTime() -
          new Date(a.session_date).getTime()
      );
  }, [flexPackages]);

  const upcomingSessions = useMemo(() => {
    return flexPackages
      .flatMap((p) =>
        p.sessions
          .filter((s) => s.status === "scheduled")
          .map((s) => ({ ...s, packageName: p.packageName }))
      )
      .sort(
        (a, b) =>
          new Date(a.session_date).getTime() -
          new Date(b.session_date).getTime()
      )
      .slice(0, 6);
  }, [flexPackages]);

  if (loading) {
    return <ModernLoadingScreen />;
  }

  return (
    <>
      <div className="space-y-3 py-3">
        <DashboardHeader memberInfo={memberInfo} />

        <div className="grid lg:grid-cols-4 gap-3">
          <div className="xl:col-span-3 space-y-3">
            <UpcomingSessions
              sessions={upcomingSessions}
              onDelete={handleDeleteSession}
            />

            <StatsGrid stats={stats} />

            <PackagesSection
              flexPackages={flexPackages}
              expandedPackages={expandedPackages}
              togglePackageExpansion={togglePackageExpansion}
              handleDeleteSession={handleDeleteSession}
            />
          </div>
          <div className="space-y-4">
            {allCompletedSessions.length > 0 && (
              <Collapsible>
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-semibold tracking-tight">
                    Geçmiş Seanslar
                  </h3>
                  <CollapsibleTrigger className="text-xs text-muted-foreground hover:text-foreground underline underline-offset-4">
                    Göster / Gizle
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent className="mt-2">
                  <CompletedSessionsTimeline sessions={allCompletedSessions} />
                </CollapsibleContent>
              </Collapsible>
            )}
          </div>
        </div>
      </div>

      {/* Floating Action Button for New Appointment - Desktop Only */}
      <div className="hidden md:block fixed bottom-6 right-6 z-40">
        <Link href="/appointments/new">
          <Button
            size="icon"
            className="rounded-full w-12 h-12 p-0 shadow-xl hover:shadow-2xl transition-all duration-300 bg-gradient-to-r from-primary to-primary/80"
          >
            <Calendar className="w-5 h-5 text-primary-foreground" />
          </Button>
        </Link>
      </div>

      <AnimatePresence>
        {deleteConfirmation.show && (
          <ModernDeleteDialog
            isOpen={deleteConfirmation.show}
            onClose={() =>
              setDeleteConfirmation({
                show: false,
                sessionId: "",
                sessionInfo: "",
              })
            }
            onConfirm={confirmDeleteSession}
            sessionInfo={deleteConfirmation.sessionInfo || ""}
          />
        )}
      </AnimatePresence>
    </>
  );
}
