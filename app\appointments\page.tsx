"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
  getMemberActivePackages,
  getMemberSessions,
  deleteSession,
} from "@/lib/actions/database";
import type {
  Member,
  MemberFlexPackageWithDetails,
} from "@/lib/supabase/types";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import Link from "next/link";

// Import modular components
import DashboardHeader from "@/components/appointments/DashboardHeader";
import UpcomingSessions from "@/components/appointments/UpcomingSessions";
import StatsGrid from "@/components/appointments/StatsGrid";
import PackagesSection from "@/components/appointments/PackagesSection";
import CompletedSessionsTimeline from "@/components/appointments/CompletedSessionsTimeline";
import ModernLoadingScreen from "@/components/appointments/ModernLoadingScreen";
import ModernDeleteDialog from "@/components/appointments/ModernDeleteDialog";
import { Button } from "@/components/ui/button";
import { Calendar, X, ExternalLink, Plus, Package, History, CheckCircle, ChevronDown } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import Link from "next/link";

// Type definitions
interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface FlexPackageGroup {
  packageName: string;
  sessions: ServerFlexPackageSessionWithDetails[];
  memberPackage: MemberFlexPackageWithDetails;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  expiryDate: string;
}

interface FlexDashboardStats {
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  scheduledSessions: number;
  completedSessions: number;
  activePackages: number;
}

// Animation variants
const staggerChildren = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Format utilities
const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const formatTime = (timeString: string) => timeString.slice(0, 5);

// Main component
export default function FlexAppointmentsPage() {
  const [userPhone, setUserPhone] = useState<string | null>(null);

  useEffect(() => {
    const storedPhone = sessionStorage.getItem("memberPhone");
    if (storedPhone) {
      setUserPhone(storedPhone);
    }
  }, []);

  // The layout will handle phone gate, so we just render the dashboard
  if (!userPhone) {
    return null; // Layout will show phone gate
  }

  return <ModernDashboard phone={userPhone} />;
}

// Modern Dashboard Component - Refactored with modular components
function ModernDashboard({ phone }: { phone: string }) {
  const [memberInfo, setMemberInfo] = useState<Member | null>(null);
  const [flexPackages, setFlexPackages] = useState<FlexPackageGroup[]>([]);
  const [stats, setStats] = useState<FlexDashboardStats>({
    totalSessions: 0,
    usedSessions: 0,
    remainingSessions: 0,
    scheduledSessions: 0,
    completedSessions: 0,
    activePackages: 0,
  });
  const [expandedPackages, setExpandedPackages] = useState<
    Record<string, boolean>
  >({});
  const [loading, setLoading] = useState(true);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    show: boolean;
    sessionId: string;
    sessionInfo?: string;
  }>({ show: false, sessionId: "", sessionInfo: "" });

  // Optimized data fetching with useMemo
  const fetchFlexData = useCallback(async () => {
    try {
      setLoading(true);
      await autoCompletePastSessions();
      const member = await getMemberFromPhone(phone);
      if (!member) {
        toast.error("Üye bulunamadı");
        setLoading(false);
        return;
      }
      setMemberInfo(member);

      // Fetch all data in parallel
      const [activePackages, allSessions] = await Promise.all([
        getMemberActivePackages(member.id),
        getMemberSessions(member.id),
      ]);

      // Process packages with sessions in one pass
      const packagesWithSessions: FlexPackageGroup[] = activePackages.map(
        (memberPackage) => {
          const packageSessions = allSessions.filter(
            (session) => session.member_package_id === memberPackage.id
          );

          return {
            packageName: memberPackage.flex_package?.name || "Esnek Paket",
            sessions: packageSessions as ServerFlexPackageSessionWithDetails[],
            memberPackage,
            totalSessions: memberPackage.total_sessions,
            usedSessions: memberPackage.used_sessions,
            remainingSessions:
              memberPackage.total_sessions - memberPackage.used_sessions,
            expiryDate: memberPackage.expiry_date,
          };
        }
      );

      // Calculate stats in one pass
      const totalStats = packagesWithSessions.reduce(
        (acc, pkg) => {
          acc.totalSessions += pkg.totalSessions;
          acc.usedSessions += pkg.usedSessions;
          acc.remainingSessions += pkg.remainingSessions;
          acc.scheduledSessions += pkg.sessions.filter(
            (s) => s.status === "scheduled"
          ).length;
          acc.completedSessions += pkg.sessions.filter(
            (s) => s.status === "completed"
          ).length;
          return acc;
        },
        {
          totalSessions: 0,
          usedSessions: 0,
          remainingSessions: 0,
          scheduledSessions: 0,
          completedSessions: 0,
          activePackages: activePackages.length,
        }
      );

      setFlexPackages(packagesWithSessions);
      setStats(totalStats);
    } catch (err) {
      console.error("Error fetching flex data:", err);
      toast.error("Esnek paket verileri yüklenirken hata oluştu");
    } finally {
      setLoading(false);
    }
  }, [phone]);

  useEffect(() => {
    fetchFlexData();
  }, [phone, fetchFlexData]);

  const togglePackageExpansion = (packageId: string) => {
    setExpandedPackages((prev) => ({ ...prev, [packageId]: !prev[packageId] }));
  };

  const handleDeleteSession = (
    sessionId: string,
    sessionDate: string,
    sessionTime: string
  ) => {
    if (!canDeleteSession(sessionDate, sessionTime)) {
      toast.error("Randevu saatine 12 saatten az kaldığı için silinemez.");
      return;
    }
    setDeleteConfirmation({
      show: true,
      sessionId,
      sessionInfo: `${formatDate(sessionDate)} - ${formatTime(sessionTime)}`,
    });
  };

  const confirmDeleteSession = async () => {
    try {
      const success = await deleteSession(deleteConfirmation.sessionId);
      if (success) {
        toast.success("Randevu başarıyla silindi.");
        fetchFlexData();
      } else {
        toast.error("Randevu silinirken bir hata oluştu.");
      }
    } catch (error) {
      console.error("Error deleting session:", error);
      toast.error("Randevu silinirken bir hata oluştu.");
    } finally {
      setDeleteConfirmation({ show: false, sessionId: "", sessionInfo: "" });
    }
  };

  // Memoize completed sessions calculation
  const allCompletedSessions = useMemo(() => {
    return flexPackages
      .flatMap((p) =>
        p.sessions
          .filter((s) => s.status === "completed")
          .map((s) => ({ ...s, packageName: p.packageName }))
      )
      .sort(
        (a, b) =>
          new Date(b.session_date).getTime() -
          new Date(a.session_date).getTime()
      );
  }, [flexPackages]);

  const upcomingSessions = useMemo(() => {
    return flexPackages
      .flatMap((p) =>
        p.sessions
          .filter((s) => s.status === "scheduled")
          .map((s) => ({ ...s, packageName: p.packageName }))
      )
      .sort(
        (a, b) =>
          new Date(a.session_date).getTime() -
          new Date(b.session_date).getTime()
      )
      .slice(0, 6);
  }, [flexPackages]);

  if (loading) {
    return <ModernLoadingScreen />;
  }

  return (
    <>
      {/* Compact Header */}
      <div className="flex items-center justify-between py-2 mb-4">
        <div>
          <h1 className="text-lg font-semibold text-foreground">Randevu Paneli</h1>
          <p className="text-xs text-muted-foreground">
            {memberInfo?.name || "Üye"} • {new Date().toLocaleDateString("tr-TR")}
          </p>
        </div>
        <Button asChild size="sm" className="h-8 px-3">
          <Link href="/appointments/new">
            <Calendar className="w-4 h-4 mr-1.5" /> Yeni
          </Link>
        </Button>
      </div>

      {/* Compact Stats Row */}
      <div className="grid grid-cols-4 gap-2 mb-4">
        <div className="bg-card border rounded-lg p-2.5 text-center">
          <div className="text-lg font-bold text-emerald-600">{stats.remainingSessions}</div>
          <div className="text-xs text-muted-foreground">Kalan</div>
        </div>
        <div className="bg-card border rounded-lg p-2.5 text-center">
          <div className="text-lg font-bold text-blue-600">{stats.scheduledSessions}</div>
          <div className="text-xs text-muted-foreground">Planlanmış</div>
        </div>
        <div className="bg-card border rounded-lg p-2.5 text-center">
          <div className="text-lg font-bold text-violet-600">{stats.completedSessions}</div>
          <div className="text-xs text-muted-foreground">Tamamlanmış</div>
        </div>
        <div className="bg-card border rounded-lg p-2.5 text-center">
          <div className="text-lg font-bold text-amber-600">{stats.activePackages}</div>
          <div className="text-xs text-muted-foreground">Aktif Paket</div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid lg:grid-cols-3 gap-4">
        {/* Left Column - Upcoming Sessions */}
        <div className="lg:col-span-2 space-y-3">
          {/* Upcoming Sessions - Compact */}
          <div className="bg-card border rounded-lg">
            <div className="flex items-center justify-between p-3 border-b">
              <h2 className="text-sm font-semibold">Yaklaşan Randevular</h2>
              <span className="text-xs text-muted-foreground">{upcomingSessions.length} randevu</span>
            </div>
            <div className="p-2">
              {upcomingSessions.length === 0 ? (
                <div className="text-center py-6">
                  <Calendar className="w-8 h-8 mx-auto text-muted-foreground/40 mb-2" />
                  <p className="text-sm text-muted-foreground mb-3">Yaklaşan randevun yok</p>
                  <Button asChild size="sm">
                    <Link href="/appointments/new">Randevu Al</Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-1">
                  {upcomingSessions.slice(0, 3).map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-2 rounded-md hover:bg-secondary/50 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-md flex items-center justify-center">
                          <Calendar className="w-4 h-4 text-blue-600" />
                        </div>
                        <div>
                          <div className="text-sm font-medium">{formatDate(session.session_date)}</div>
                          <div className="text-xs text-muted-foreground">{formatTime(session.session_time)} • {session.packageName}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {canDeleteSession(session.session_date, session.session_time) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => handleDeleteSession(session.id, session.session_date, session.session_time)}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        )}
                        <Button asChild variant="ghost" size="sm" className="h-6 px-2">
                          <Link href={`/appointments/${session.id}`}>
                            <ExternalLink className="w-3 h-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                  {upcomingSessions.length > 3 && (
                    <div className="text-center pt-2">
                      <Button asChild variant="ghost" size="sm" className="text-xs">
                        <Link href="/appointments/calendar">+{upcomingSessions.length - 3} daha fazla</Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Active Packages - Compact */}
          <div className="bg-card border rounded-lg">
            <div className="flex items-center justify-between p-3 border-b">
              <h2 className="text-sm font-semibold">Aktif Paketler</h2>
              <span className="text-xs text-muted-foreground">{flexPackages.length} paket</span>
            </div>
            <div className="p-2">
              {flexPackages.length === 0 ? (
                <div className="text-center py-6">
                  <Package className="w-8 h-8 mx-auto text-muted-foreground/40 mb-2" />
                  <p className="text-sm text-muted-foreground mb-3">Aktif paketiniz yok</p>
                  <Button asChild size="sm">
                    <Link href="/appointments/new">Paket Seç</Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  {flexPackages.map((pkg) => {
                    const progress = (pkg.usedSessions / pkg.totalSessions) * 100;
                    const remainingDays = Math.max(0, Math.ceil((new Date(pkg.expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)));

                    return (
                      <div key={pkg.memberPackage.id} className="p-3 rounded-md border bg-secondary/20">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-sm font-medium">{pkg.packageName}</h3>
                          <span className="text-xs text-muted-foreground">{remainingDays} gün kaldı</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex-1">
                            <div className="flex justify-between text-xs mb-1">
                              <span>{pkg.remainingSessions} kalan</span>
                              <span>{pkg.totalSessions} toplam</span>
                            </div>
                            <div className="w-full h-1.5 bg-secondary rounded-full overflow-hidden">
                              <div
                                className="h-full bg-primary rounded-full transition-all duration-300"
                                style={{ width: `${progress}%` }}
                              />
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2"
                            onClick={() => togglePackageExpansion(pkg.memberPackage.id)}
                          >
                            <ChevronDown className={cn("w-3 h-3 transition-transform", expandedPackages[pkg.memberPackage.id] && "rotate-180")} />
                          </Button>
                        </div>

                        {expandedPackages[pkg.memberPackage.id] && (
                          <div className="mt-3 pt-2 border-t border-border/30">
                            <div className="space-y-1">
                              {pkg.sessions.filter(s => s.status === "scheduled").map((session) => (
                                <div key={session.id} className="flex items-center justify-between text-xs p-1.5 rounded bg-background/50">
                                  <span>{formatDate(session.session_date)} {formatTime(session.session_time)}</span>
                                  {canDeleteSession(session.session_date, session.session_time) && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-4 w-4 p-0"
                                      onClick={() => handleDeleteSession(session.id, session.session_date, session.session_time)}
                                    >
                                      <X className="w-2.5 h-2.5" />
                                    </Button>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Recent Activity */}
        <div className="space-y-3">
          {/* Quick Actions */}
          <div className="bg-card border rounded-lg p-3">
            <h3 className="text-sm font-semibold mb-3">Hızlı İşlemler</h3>
            <div className="space-y-2">
              <Button asChild variant="outline" size="sm" className="w-full justify-start h-8">
                <Link href="/appointments/new">
                  <Plus className="w-3 h-3 mr-2" /> Yeni Randevu
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm" className="w-full justify-start h-8">
                <Link href="/appointments/calendar">
                  <Calendar className="w-3 h-3 mr-2" /> Takvim Görünümü
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm" className="w-full justify-start h-8">
                <Link href="/appointments/history">
                  <History className="w-3 h-3 mr-2" /> Geçmiş Randevular
                </Link>
              </Button>
            </div>
          </div>

          {/* Recent Completed Sessions */}
          {allCompletedSessions.length > 0 && (
            <div className="bg-card border rounded-lg">
              <div className="flex items-center justify-between p-3 border-b">
                <h3 className="text-sm font-semibold">Son Tamamlananlar</h3>
                <Button asChild variant="ghost" size="sm" className="h-6 px-2 text-xs">
                  <Link href="/appointments/history">Tümü</Link>
                </Button>
              </div>
              <div className="p-2">
                <div className="space-y-1">
                  {allCompletedSessions.slice(0, 4).map((session) => (
                    <div key={session.id} className="flex items-center gap-2 p-1.5 rounded text-xs">
                      <div className="w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-md flex items-center justify-center">
                        <CheckCircle className="w-3 h-3 text-emerald-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{formatDate(session.session_date)}</div>
                        <div className="text-muted-foreground truncate">{session.packageName}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>

      {/* Floating Action Button for New Appointment - Desktop Only */}
      <div className="hidden md:block fixed bottom-6 right-6 z-40">
        <Link href="/appointments/new">
          <Button
            size="icon"
            className="rounded-full w-12 h-12 p-0 shadow-xl hover:shadow-2xl transition-all duration-300 bg-gradient-to-r from-primary to-primary/80"
          >
            <Calendar className="w-5 h-5 text-primary-foreground" />
          </Button>
        </Link>
      </div>

      <AnimatePresence>
        {deleteConfirmation.show && (
          <ModernDeleteDialog
            isOpen={deleteConfirmation.show}
            onClose={() =>
              setDeleteConfirmation({
                show: false,
                sessionId: "",
                sessionInfo: "",
              })
            }
            onConfirm={confirmDeleteSession}
            sessionInfo={deleteConfirmation.sessionInfo || ""}
          />
        )}
      </AnimatePresence>
    </>
  );
}
