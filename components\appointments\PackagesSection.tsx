"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import Link from "next/link";
import {
  Package,
  Plus,
  Calendar,
  ChevronDown,
  Target,
  TrendingUp,
  Clock,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import SessionItem from "./SessionItem";
import type {
  Member,
  MemberFlexPackageWithDetails,
} from "@/lib/supabase/types";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface FlexPackageGroup {
  packageName: string;
  sessions: ServerFlexPackageSessionWithDetails[];
  memberPackage: MemberFlexPackageWithDetails;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  expiryDate: string;
}

interface PackagesSectionProps {
  flexPackages: FlexPackageGroup[];
  expandedPackages: Record<string, boolean>;
  togglePackageExpansion: (packageId: string) => void;
  handleDeleteSession: (
    sessionId: string,
    sessionDate: string,
    sessionTime: string
  ) => void;
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const calculateRemainingDays = (expiryDate: string): number => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const diffTime = expiry.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

export default function PackagesSection({
  flexPackages,
  expandedPackages,
  togglePackageExpansion,
  handleDeleteSession,
}: PackagesSectionProps) {
  return (
    <motion.section variants={fadeInUp} className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-foreground">Aktif Paketler</h2>
          {/* compact: remove verbose subheading */}
        </div>
      </div>

      {flexPackages.length === 0 ? (
        <div className="relative overflow-hidden rounded-lg border bg-card p-4">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 mx-auto bg-secondary rounded-md flex items-center justify-center">
              <Package className="w-6 h-6 text-foreground/80" />
            </div>
            <div className="space-y-2">
              <h3 className="text-base font-semibold text-foreground">
                Henüz Aktif Paketiniz Yok
              </h3>
              <p className="text-muted-foreground text-sm max-w-md mx-auto">
                Randevu alabilmek için bir esnek paket seçin.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center pt-2">
              <Button size="sm" className="px-4 rounded-md" asChild>
                <Link href="/appointments/new">
                  <Plus className="w-4 h-4 mr-2" />
                  Yeni Randevu Al
                </Link>
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="grid gap-4">
          {flexPackages.map((flexPackage, index) => {
            const isExpanded =
              expandedPackages[flexPackage.memberPackage.id] ?? true;
            const progress =
              (flexPackage.usedSessions / flexPackage.totalSessions) * 100;
            const scheduledSessions = flexPackage.sessions.filter(
              (s) => s.status === "scheduled"
            );
            const completedThisWeek = flexPackage.sessions.filter(
              (s) =>
                s.status === "completed" &&
                new Date(s.session_date) >=
                  new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            ).length;
            const remainingDays = calculateRemainingDays(
              flexPackage.expiryDate
            );

            // Determine urgency color for expiration
            const getExpiryColor = () => {
              if (remainingDays <= 7) return "text-destructive";
              if (remainingDays <= 30)
                return "text-amber-600 dark:text-amber-400";
              return "text-emerald-600 dark:text-emerald-400";
            };

            return (
              <motion.div
                key={flexPackage.memberPackage.id}
                variants={fadeInUp}
                transition={{ delay: index * 0.1 }}
                className="group"
              >
                <Collapsible
                  open={isExpanded}
                  onOpenChange={() =>
                    togglePackageExpansion(flexPackage.memberPackage.id)
                  }
                  className="overflow-hidden rounded-lg border bg-card transition-all duration-200"
                >
                  <CollapsibleTrigger className="w-full p-3 hover:bg-secondary/10 transition-colors">
                    <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-primary via-primary/80 to-primary/60 rounded-xl flex items-center justify-center text-primary-foreground shadow-md">
                            <Package className="w-8 h-8" />
                          </div>
                        </div>
                        <div className="text-left space-y-2">
                          <h3 className="text-lg font-semibold text-foreground">
                            {flexPackage.packageName}
                          </h3>
                          <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                            <span className="flex items-center gap-2">
                              <Target className="w-5 h-5" />
                              {flexPackage.remainingSessions} /{" "}
                              {flexPackage.totalSessions} seans
                            </span>

                            <span
                              className={`flex items-center gap-2 font-medium ${getExpiryColor()}`}
                            >
                              <Clock className="w-5 h-5" />
                              {remainingDays} gün kaldı
                            </span>
                          </div>
                          <div className="w-40 h-2 bg-secondary rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-primary to-primary/70 rounded-full transition-all duration-500 ease-out"
                              style={{ width: `${progress}%` }}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap items-center gap-5 w-full lg:w-auto">
                        <div className="flex flex-wrap items-center gap-4">
                          <Badge
                            variant="secondary"
                            className="bg-primary/10 text-primary border-primary/20 font-medium py-1 px-3 text-sm"
                          >
                            {scheduledSessions.length} Planlanmış
                          </Badge>
                          <div className="space-y-1">
                            <p className="text-sm text-muted-foreground">
                              Son kullanma: {formatDate(flexPackage.expiryDate)}
                            </p>
                            <p
                              className={`text-sm font-medium ${getExpiryColor()}`}
                            >
                              {remainingDays} gün süre kaldı
                            </p>
                          </div>
                        </div>
                        <ChevronDown
                          className={cn(
                            "w-5 h-5 text-muted-foreground transition-all duration-300 group-hover:text-foreground ml-auto lg:ml-0",
                            isExpanded && "rotate-180"
                          )}
                        />
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="border-t border-border/30 bg-secondary/5">
                    <div className="p-3 space-y-3">
                      {scheduledSessions.length === 0 ? (
                        <div className="text-center py-6 space-y-3">
                          <Calendar className="w-10 h-10 mx-auto text-muted-foreground/40" />
                          <div>
                            <p className="font-medium text-foreground text-base">
                              Planlanmış randevu bulunmuyor
                            </p>
                            <p className="text-muted-foreground">
                              Yeni bir randevu alarak başlayın
                            </p>
                          </div>
                          <Link href="/appointments/new">
                            <Button variant="outline" size="md">
                              <Plus className="w-5 h-5 mr-2" />
                              Randevu Al
                            </Button>
                          </Link>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <h4 className="font-semibold text-foreground flex items-center gap-2 text-base">
                            <Calendar className="w-4 h-4 text-primary" />
                            Yaklaşan Randevular
                          </h4>
                          {scheduledSessions
                            .sort(
                              (a, b) =>
                                new Date(a.session_date).getTime() -
                                new Date(b.session_date).getTime()
                            )
                            .map((session, sessionIndex) => (
                              <SessionItem
                                compact
                                key={session.id}
                                session={session}
                                packageName={flexPackage.packageName}
                                onDelete={() =>
                                  handleDeleteSession(
                                    session.id,
                                    session.session_date,
                                    session.session_time
                                  )
                                }
                                canDelete={canDeleteSession(
                                  session.session_date,
                                  session.session_time
                                )}
                                index={sessionIndex}
                              />
                            ))}
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </motion.div>
            );
          })}
        </div>
      )}
    </motion.section>
  );
}
