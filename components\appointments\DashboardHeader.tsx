"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { Member } from "@/lib/supabase/types";
import { Calendar } from "lucide-react";

export default function DashboardHeader({
  memberInfo,
}: {
  memberInfo: Member | null;
}) {
  return (
    <section aria-labelledby="dashboard-heading" className="pb-1">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-end sm:justify-between">
        <div>
          <h1
            id="dashboard-heading"
            className="text-xl sm:text-2xl font-semibold tracking-tight"
          >
            Rand<PERSON>u Paneli
          </h1>
          {/* Compact: remove verbose subheading to reduce vertical space */}
          {/* compact header: subheading removed intentionally */}
        </div>
        <div className="flex items-center gap-3">
          <Button asChild size="md" variant="default" className="rounded-md">
            <Link href="/appointments/new">
              <Calendar className="w-5 h-5 mr-2" /> <PERSON><PERSON>
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
