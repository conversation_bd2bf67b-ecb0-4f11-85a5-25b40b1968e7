"use client";

import { motion } from "framer-motion";

import { Button } from "@/components/ui/button";
import { History } from "lucide-react";
import SessionItem from "./SessionItem";
import type { Member } from "@/lib/supabase/types";
import Link from "next/link";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface CompletedSessionsTimelineProps {
  sessions: ServerFlexPackageSessionWithDetails[];
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

export default function CompletedSessionsTimeline({
  sessions,
}: CompletedSessionsTimelineProps) {
  return (
    <motion.section variants={fadeInUp} className="space-y-3">
      <div className="rounded-lg border bg-card p-3">
        {sessions.length === 0 ? (
          <div className="text-center py-6 space-y-2">
            <History className="w-6 h-6 mx-auto text-muted-foreground/60" />
            <div className="text-sm text-muted-foreground">
              Tamamlanmış randevunuz yok
            </div>
            <Button asChild size="sm">
              <Link href="/appointments/new">Randevu Al</Link>
            </Button>
          </div>
        ) : (
          <div className="grid gap-2.5">
            {sessions.slice(0, 5).map((session, index) => (
              <SessionItem
                key={session.id}
                session={session}
                isHistorical={true}
                index={index}
                compact
              />
            ))}
            {sessions.length > 5 && (
              <div className="text-center pt-2">
                <Button asChild variant="outline" size="sm">
                  <Link href="/appointments/history">Tüm Geçmişi Gör</Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </motion.section>
  );
}
